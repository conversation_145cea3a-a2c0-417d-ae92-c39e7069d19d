# AMS Microservices Architecture

This document describes the microservices architecture for the AMS (Ashara Management System) application.

## Architecture Overview

The AMS application has been transformed from a monolithic architecture to a microservices architecture. Each module now runs as an independent service on its own port.

## Services

| Service | Port | Description | Endpoint |
|---------|------|-------------|----------|
| Main Gateway | 8000 | Handles public API and webhook routes | `/api/v1`, `/api/webhook` |
| Global Masters | 8001 | Manages global master data | `/api/global-master` |
| Hierarchy | 8002 | Manages organizational hierarchy | `/api/hierarchy`, `/api/hr/interest` |
| Communication | 8003 | Handles communication features | `/api/communication` |
| Ashara Guide | 8004 | Manages ashara guide functionality | `/api/ashara-guide` |
| Zones Capacity | 8005 | Manages zones and capacity | `/api/zones-capacity` |
| Task Management | 8006 | Handles task management | `/api/task-management` |
| Accommodation | 8007 | Manages accommodation services | `/api/accomodation` |
| Document Manager | 8008 | Handles document management | `/api/document-manager` |
| Reports | 8009 | Generates and manages reports | `/api/report` |
| Survey | 8010 | Manages survey functionality | `/api/survey` |
| Analytics Log | 8011 | Handles analytics and logging | `/api/activity` |

## Running the Services

### Prerequisites

1. Node.js installed
2. MongoDB connection configured in `.env`
3. All dependencies installed: `npm install`

### Running Individual Services

You can run each service individually using the following commands:

```bash
# Main Gateway (Public API & Webhooks)
npm run start:gateway

# Individual Microservices
npm run start:global-masters
npm run start:hierarchy
npm run start:communication
npm run start:ashara-guide
npm run start:zones-capacity
npm run start:task-management
npm run start:accommodation
npm run start:document-manager
npm run start:reports
npm run start:survey
npm run start:analytics-log
```

### Running All Services

To start all services at once:

```bash
npm run start:all
# or
npm run dev:all
```

This will start all microservices simultaneously. Press `Ctrl+C` to stop all services.

### Health Checks

Each service provides a health check endpoint:

- `GET http://localhost:{PORT}/health`

Example:
- `GET http://localhost:8001/health` - Global Masters service health
- `GET http://localhost:8002/health` - Hierarchy service health

## Environment Configuration

The following environment variables control the microservices ports:

```env
# Microservice Ports Configuration
SERVICE_HOST=localhost
GATEWAY_PORT=8000
GLOBAL_MASTERS_PORT=8001
HIERARCHY_PORT=8002
COMMUNICATION_PORT=8003
ASHARA_GUIDE_PORT=8004
ZONES_CAPACITY_PORT=8005
TASK_MANAGEMENT_PORT=8006
ACCOMMODATION_PORT=8007
DOCUMENT_MANAGER_PORT=8008
REPORTS_PORT=8009
SURVEY_PORT=8010
ANALYTICS_LOG_PORT=8011
PUBLIC_PORT=8012
WEBHOOK_PORT=8013
```

## Database Strategy

All microservices currently share the same MongoDB database connection. Each service connects to the same database but handles different collections based on their domain.

## Authentication & Authorization

All microservices (except the main gateway for public APIs) use the same authentication and authorization middleware:
- `authGuard` - Validates JWT tokens
- `roleGuard` - Checks user permissions
- `validateApiKey` - Validates API keys for public endpoints

## Development Notes

1. **Shared Dependencies**: All services share the same `node_modules` and dependencies
2. **Shared Utilities**: Common utilities, middlewares, and constants are shared across services
3. **Database**: Single database with shared connection configuration
4. **Static Assets**: All services can serve static assets from the shared `/assets` directory

## Migration from Monolith

The transformation involved:

1. Creating individual `app.js` files for each module
2. Configuring separate ports for each service
3. Updating the main application to handle only public APIs and webhooks
4. Adding npm scripts for running individual and all services
5. Maintaining shared database and utility functions

## Future Enhancements

Consider these improvements for production:

1. **Process Management**: Use PM2 or similar for production process management
2. **Load Balancing**: Implement load balancing for high-traffic services
3. **Service Discovery**: Add service discovery mechanism
4. **API Gateway**: Consider implementing a proper API gateway
5. **Database Separation**: Evaluate separating databases per service domain
6. **Containerization**: Docker containers for each service
7. **Monitoring**: Add comprehensive monitoring and logging
