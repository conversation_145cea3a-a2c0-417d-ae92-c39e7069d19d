const express = require("express");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const { CORS_ORIGIN, NODE_ENV, ACCOMMODATION_PORT } = require("../../constants");
const routes = require("./routes");
const { authGuard, roleGuard } = require("../../middlewares/guard.middleware");
const { connectDB } = require("../../db");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));

app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});


// Accommodation routes
app.use("/api/accomodation", authGuard, roleGuard, routes);

app.get("/", (req, res) => {
  res.send(`Accommodation Service is running on port: ${ACCOMMODATION_PORT}`);
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({ 
    status: "healthy", 
    service: "accommodation",
    port: ACCOMMODATION_PORT,
    timestamp: new Date().toISOString()
  });
});

// Start server
const startServer = async () => {
  try {
    await connectDB();
    app.listen(ACCOMMODATION_PORT, () => {
      console.log(`Node environment: ${NODE_ENV}`);
      console.log(`Accommodation Service running on port: ${ACCOMMODATION_PORT}`);
    });
  } catch (err) {
    console.log(`DB Error: ${err.message}`);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

module.exports = app;
