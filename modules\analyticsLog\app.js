const express = require("express");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const { CORS_ORIGIN, NODE_ENV, ANALYTICS_LOG_PORT } = require("../../constants");
const routes = require("./routes");
const { authGuard, roleGuard } = require("../../middlewares/guard.middleware");
const { connectDB } = require("../../db");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));

app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});


// Analytics Log routes
app.use("/api/activity", authGuard, roleGuard, routes);

app.get("/", (req, res) => {
  res.send(`Analytics Log Service is running on port: ${ANALYTICS_LOG_PORT}`);
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({ 
    status: "healthy", 
    service: "analytics-log",
    port: ANALYTICS_LOG_PORT,
    timestamp: new Date().toISOString()
  });
});

// Start server
const startServer = async () => {
  try {
    await connectDB();
    app.listen(ANALYTICS_LOG_PORT, () => {
      console.log(`Node environment: ${NODE_ENV}`);
      console.log(`Analytics Log Service running on port: ${ANALYTICS_LOG_PORT}`);
    });
  } catch (err) {
    console.log(`DB Error: ${err.message}`);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

module.exports = app;
