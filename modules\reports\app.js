const express = require("express");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const { CORS_ORIGIN, NODE_ENV, REPORTS_PORT } = require("../../constants");
const routes = require("./routes");
const { authGuard, roleGuard } = require("../../middlewares/guard.middleware");
const { connectDB } = require("../../db");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));
app.use(express.static("public"));
app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});
app.use("/assets", express.static(path.join(__dirname, "../../assets")));
app.use(
  compression({
    level: 6,
    filter: (req, res) => {
      if (req.headers["x-no-compression"]) {
        return false;
      }
      return compression.filter(req, res);
    },
  })
);

// Reports routes
app.use("/api/report", authGuard, roleGuard, routes);

app.get("/", (req, res) => {
  res.send(`Reports Service is running on port: ${REPORTS_PORT}`);
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({ 
    status: "healthy", 
    service: "reports",
    port: REPORTS_PORT,
    timestamp: new Date().toISOString()
  });
});

// Start server
const startServer = async () => {
  try {
    await connectDB();
    app.listen(REPORTS_PORT, () => {
      console.log(`Node environment: ${NODE_ENV}`);
      console.log(`Reports Service running on port: ${REPORTS_PORT}`);
    });
  } catch (err) {
    console.log(`DB Error: ${err.message}`);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

module.exports = app;
