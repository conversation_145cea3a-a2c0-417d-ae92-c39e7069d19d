{"name": "ams_backend", "version": "1.0.0", "main": "server.js", "scripts": {"server": "node --max-old-space-size=2560 server.js", "microservices": "node start-all-services.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.749.0", "@aws-sdk/lib-storage": "^3.750.0", "@aws-sdk/s3-request-presigner": "^3.750.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bullmq": "^5.41.3", "cors": "^2.8.5", "cron": "^4.0.0", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "express": "^4.21.2", "fast-xml-parser": "^4.5.1", "haversine-distance": "^1.2.4", "html-pdf": "^3.0.1", "ioredis": "^5.5.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mongodb": "^6.13.0", "mongoose": "^8.9.2", "mongoose-aggregate-paginate-v2": "^1.1.4", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.3", "onesignal-node": "^3.4.0", "sanitize-html": "^2.14.0", "swagger-ui-express": "^5.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"compression": "^1.7.5", "nodemon": "^3.1.9"}}