const { spawn } = require('child_process');
const path = require('path');

// Define all microservices
const services = [
  { name: 'Gateway', script: 'server.js', port: process.env.GATEWAY_PORT || 8000 },
  { name: 'Global Masters', script: 'modules/globalMasters/app.js', port: process.env.GLOBAL_MASTERS_PORT || 8001 },
  { name: 'Hierarchy', script: 'modules/hierarchy/app.js', port: process.env.HIERARCHY_PORT || 8002 },
  { name: 'Communication', script: 'modules/communication/app.js', port: process.env.COMMUNICATION_PORT || 8003 },
  { name: 'Ashara Guide', script: 'modules/asharaGuide/app.js', port: process.env.ASHARA_GUIDE_PORT || 8004 },
  { name: 'Zones Capacity', script: 'modules/zonesCapacity/app.js', port: process.env.ZONES_CAPACITY_PORT || 8005 },
  { name: 'Task Management', script: 'modules/taskManagement/app.js', port: process.env.TASK_MANAGEMENT_PORT || 8006 },
  { name: 'Accommodation', script: 'modules/accomodation/app.js', port: process.env.ACCOMMODATION_PORT || 8007 },
  { name: 'Document Manager', script: 'modules/documentManager/app.js', port: process.env.DOCUMENT_MANAGER_PORT || 8008 },
  { name: 'Reports', script: 'modules/reports/app.js', port: process.env.REPORTS_PORT || 8009 },
  { name: 'Survey', script: 'modules/survey/app.js', port: process.env.SURVEY_PORT || 8010 },
  { name: 'Analytics Log', script: 'modules/analyticsLog/app.js', port: process.env.ANALYTICS_LOG_PORT || 8011 }
];

const processes = [];

// Function to start a service
function startService(service) {
  console.log(`Starting ${service.name} on port ${service.port}...`);
  
  const child = spawn('node', ['--max-old-space-size=2560', service.script], {
    stdio: 'inherit',
    cwd: __dirname
  });

  child.on('error', (err) => {
    console.error(`Failed to start ${service.name}:`, err);
  });

  child.on('exit', (code) => {
    console.log(`${service.name} exited with code ${code}`);
  });

  processes.push({ name: service.name, process: child });
  return child;
}

// Function to stop all services
function stopAllServices() {
  console.log('\nStopping all services...');
  processes.forEach(({ name, process }) => {
    console.log(`Stopping ${name}...`);
    process.kill('SIGTERM');
  });
  process.exit(0);
}

// Handle graceful shutdown
process.on('SIGINT', stopAllServices);
process.on('SIGTERM', stopAllServices);

// Start all services
console.log('Starting AMS Microservices...\n');
services.forEach(service => {
  startService(service);
  // Add a small delay between starting services
  setTimeout(() => {}, 1000);
});

console.log('\nAll services started. Press Ctrl+C to stop all services.\n');
console.log('Service URLs:');
services.forEach(service => {
  console.log(`${service.name}: http://localhost:${service.port}`);
});
console.log('');
